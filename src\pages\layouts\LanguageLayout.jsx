import { useEffect } from 'react';
import { Outlet, useParams, useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { supportedLanguages, defaultLanguage, getLanguageFromPath } from '../../i18n/index.js';

export default function LanguageLayout() {
  const { i18n } = useTranslation();
  const { lang } = useParams();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // If we're at root path, redirect to default language
    if (location.pathname === '/') {
      navigate(`/${defaultLanguage}`, { replace: true });
      return;
    }

    // If no language in URL, redirect to default language
    if (!lang) {
      const detectedLang = getLanguageFromPath(location.pathname) || defaultLanguage;
      const pathWithoutLang = location.pathname;
      const newPath = `/${detectedLang}${pathWithoutLang === '/' ? '' : pathWithoutLang}${location.search}`;
      navigate(newPath, { replace: true });
      return;
    }

    // If language is not supported, redirect to default language
    if (!Object.keys(supportedLanguages).includes(lang)) {
      const pathWithoutLang = location.pathname.replace(`/${lang}`, '') || '';
      const newPath = `/${defaultLanguage}${pathWithoutLang}${location.search}`;
      navigate(newPath, { replace: true });
      return;
    }

    // Change i18n language if different from current
    if (i18n.language !== lang) {
      i18n.changeLanguage(lang);
    }
  }, [lang, i18n, navigate, location]);

  // Don't render anything if we're redirecting
  if (location.pathname === '/' || !lang || !Object.keys(supportedLanguages).includes(lang)) {
    return null;
  }

  return <Outlet />;
}

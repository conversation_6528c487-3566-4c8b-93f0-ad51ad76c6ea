{"validation": {"required": "This field is required", "email": "Please enter a valid email address", "password": "Password must be at least 8 characters long", "confirmPassword": "Passwords do not match", "phone": "Please enter a valid phone number", "url": "Please enter a valid URL", "number": "Please enter a valid number", "min": "Minimum value is {{min}}", "max": "Maximum value is {{max}}", "minLength": "Minimum length is {{min}} characters", "maxLength": "Maximum length is {{max}} characters", "pattern": "Please enter a valid format", "unique": "This value already exists", "fileSize": "File size must be less than {{size}}MB", "fileType": "Invalid file type. Allowed types: {{types}}"}, "placeholders": {"enterName": "Enter your name", "enterEmail": "Enter your email", "enterPassword": "Enter your password", "confirmPassword": "Confirm your password", "enterPhone": "Enter your phone number", "enterAddress": "Enter your address", "enterCity": "Enter your city", "selectCountry": "Select your country", "selectRegion": "Select region", "selectLanguage": "Select language", "selectCategory": "Select category", "enterTitle": "Enter title", "enterDescription": "Enter description", "enterMessage": "Enter your message", "enterSubject": "Enter subject", "searchProducts": "Search products...", "searchShops": "Search shops...", "searchBlogs": "Search blog posts...", "enterKeywords": "Enter keywords", "enterPrice": "Enter price", "enterQuantity": "Enter quantity", "selectDate": "Select date", "selectTime": "Select time", "uploadFile": "Upload file", "chooseFile": "Choose file", "dragDropFile": "Drag and drop file here"}, "labels": {"personalInfo": "Personal Information", "contactInfo": "Contact Information", "billingInfo": "Billing Information", "shippingInfo": "Shipping Information", "paymentInfo": "Payment Information", "accountInfo": "Account Information", "productInfo": "Product Information", "orderInfo": "Order Information", "required": "Required", "optional": "Optional", "recommended": "Recommended", "advanced": "Advanced", "basic": "Basic", "general": "General", "specific": "Specific", "public": "Public", "private": "Private", "visible": "Visible", "hidden": "Hidden", "enabled": "Enabled", "disabled": "Disabled", "active": "Active", "inactive": "Inactive", "available": "Available", "unavailable": "Unavailable", "inStock": "In Stock", "outOfStock": "Out of Stock", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "completed": "Completed", "cancelled": "Cancelled", "processing": "Processing", "shipped": "Shipped", "delivered": "Delivered", "returned": "Returned", "refunded": "Refunded"}, "messages": {"saveSuccess": "Changes saved successfully", "saveError": "Failed to save changes", "deleteSuccess": "Item deleted successfully", "deleteError": "Failed to delete item", "uploadSuccess": "File uploaded successfully", "uploadError": "Failed to upload file", "sendSuccess": "Message sent successfully", "sendError": "Failed to send message", "loginSuccess": "Login successful", "loginError": "<PERSON><PERSON> failed", "logoutSuccess": "Logout successful", "registerSuccess": "Registration successful", "registerError": "Registration failed", "passwordResetSuccess": "Password reset email sent", "passwordResetError": "Failed to send password reset email", "passwordChangeSuccess": "Password changed successfully", "passwordChangeError": "Failed to change password", "profileUpdateSuccess": "Profile updated successfully", "profileUpdateError": "Failed to update profile", "orderPlaceSuccess": "Order placed successfully", "orderPlaceError": "Failed to place order", "paymentSuccess": "Payment processed successfully", "paymentError": "Payment failed", "addToCartSuccess": "Item added to cart", "addToCartError": "Failed to add item to cart", "removeFromCartSuccess": "Item removed from cart", "removeFromCartError": "Failed to remove item from cart", "addToWishlistSuccess": "Item added to wishlist", "addToWishlistError": "Failed to add item to wishlist", "removeFromWishlistSuccess": "Item removed from wishlist", "removeFromWishlistError": "Failed to remove item from wishlist", "subscribeSuccess": "Subscription successful", "subscribeError": "Subscription failed", "unsubscribeSuccess": "Unsubscribed successfully", "unsubscribeError": "Failed to unsubscribe", "confirmAction": "Are you sure you want to perform this action?", "confirmDelete": "Are you sure you want to delete this item?", "confirmLogout": "Are you sure you want to logout?", "unsavedChanges": "You have unsaved changes. Are you sure you want to leave?", "sessionExpired": "Your session has expired. Please login again.", "accessDenied": "Access denied. You don't have permission to perform this action.", "networkError": "Network error. Please check your connection and try again.", "serverError": "Server error. Please try again later.", "maintenanceMode": "The system is currently under maintenance. Please try again later.", "featureNotAvailable": "This feature is not available at the moment.", "comingSoon": "This feature is coming soon.", "underDevelopment": "This feature is under development."}}
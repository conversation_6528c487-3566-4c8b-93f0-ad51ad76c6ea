{"general": {"somethingWentWrong": "Something went wrong", "tryAgain": "Please try again", "contactSupport": "Contact support if the problem persists", "unexpectedError": "An unexpected error occurred", "networkError": "Network connection error", "serverError": "Server error", "timeoutError": "Request timeout", "notFound": "Page not found", "accessDenied": "Access denied", "unauthorized": "Unauthorized access", "forbidden": "Forbidden", "badRequest": "Bad request", "internalServerError": "Internal server error", "serviceUnavailable": "Service unavailable", "maintenanceMode": "System under maintenance"}, "validation": {"invalidEmail": "Invalid email address", "invalidPassword": "Invalid password", "passwordTooShort": "Password is too short", "passwordTooWeak": "Password is too weak", "passwordsDoNotMatch": "Passwords do not match", "invalidPhoneNumber": "Invalid phone number", "invalidUrl": "Invalid URL", "invalidDate": "Invalid date", "invalidTime": "Invalid time", "invalidNumber": "Invalid number", "valueTooSmall": "Value is too small", "valueTooLarge": "Value is too large", "textTooShort": "Text is too short", "textTooLong": "Text is too long", "invalidFormat": "Invalid format", "requiredField": "This field is required", "invalidSelection": "Invalid selection", "fileTooBig": "File is too big", "invalidFileType": "Invalid file type", "uploadFailed": "File upload failed"}, "auth": {"loginFailed": "<PERSON><PERSON> failed", "invalidCredentials": "Invalid email or password", "accountLocked": "Account is locked", "accountDisabled": "Account is disabled", "accountNotVerified": "Account is not verified", "emailNotVerified": "Email is not verified", "sessionExpired": "Session has expired", "logoutFailed": "Logout failed", "registrationFailed": "Registration failed", "emailAlreadyExists": "Email already exists", "usernameAlreadyExists": "Username already exists", "weakPassword": "Password is too weak", "passwordResetFailed": "Password reset failed", "invalidResetToken": "Invalid reset token", "resetTokenExpired": "Reset token has expired", "passwordChangeFailed": "Password change failed", "currentPasswordIncorrect": "Current password is incorrect", "twoFactorRequired": "Two-factor authentication required", "invalidTwoFactorCode": "Invalid two-factor authentication code", "twoFactorSetupFailed": "Two-factor authentication setup failed"}, "payment": {"paymentFailed": "Payment failed", "cardDeclined": "Card was declined", "insufficientFunds": "Insufficient funds", "invalidCard": "Invalid card details", "cardExpired": "Card has expired", "invalidCvv": "Invalid CVV", "paymentTimeout": "Payment timeout", "paymentCancelled": "Payment was cancelled", "refundFailed": "Refund failed", "chargebackReceived": "Chargeback received", "fraudDetected": "<PERSON><PERSON> <PERSON>", "paymentMethodNotSupported": "Payment method not supported", "currencyNotSupported": "<PERSON><PERSON><PERSON><PERSON> not supported", "amountTooSmall": "Amount is too small", "amountTooLarge": "Amount is too large"}, "order": {"orderNotFound": "Order not found", "orderCancelled": "Order was cancelled", "orderExpired": "Order has expired", "orderAlreadyProcessed": "Order already processed", "orderCannotBeCancelled": "Order cannot be cancelled", "orderCannotBeModified": "Order cannot be modified", "orderCannotBeRefunded": "Order cannot be refunded", "orderCannotBeReturned": "Order cannot be returned", "orderCannotBeShipped": "Order cannot be shipped", "orderCannotBeDelivered": "Order cannot be delivered", "orderProcessingFailed": "Order processing failed", "orderFulfillmentFailed": "Order fulfillment failed", "orderTrackingNotAvailable": "Order tracking not available", "orderStatusUpdateFailed": "Order status update failed", "orderNotificationFailed": "Order notification failed"}, "product": {"productNotFound": "Product not found", "productUnavailable": "Product is unavailable", "productOutOfStock": "Product is out of stock", "productDiscontinued": "Product has been discontinued", "productNotActive": "Product is not active", "productPriceChanged": "Product price has changed", "productDetailsUpdateFailed": "Product details update failed", "productImageUploadFailed": "Product image upload failed", "productCategoryInvalid": "Invalid product category", "productDescriptionTooLong": "Product description is too long", "productNameTooLong": "Product name is too long", "productPriceInvalid": "Invalid product price", "productQuantityInvalid": "Invalid product quantity", "productSkuAlreadyExists": "Product SKU already exists", "productCreationFailed": "Product creation failed"}, "cart": {"cartEmpty": "Cart is empty", "cartItemNotFound": "Cart item not found", "cartItemUnavailable": "Cart item is unavailable", "cartItemOutOfStock": "Cart item is out of stock", "cartItemPriceChanged": "Cart item price has changed", "cartItemQuantityExceeded": "Cart item quantity exceeded", "cartItemAddFailed": "Failed to add item to cart", "cartItemRemoveFailed": "Failed to remove item from cart", "cartItemUpdateFailed": "Failed to update cart item", "cartClearFailed": "Failed to clear cart", "cartLoadFailed": "Failed to load cart", "cartSaveFailed": "Failed to save cart", "cartSyncFailed": "Failed to sync cart", "cartExpired": "<PERSON><PERSON> has expired", "cartLimitExceeded": "Cart limit exceeded"}}
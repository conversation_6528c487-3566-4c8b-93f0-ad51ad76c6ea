import {Outlet} from "react-router-dom";
import {QueryClient, QueryClientProvider} from "react-query";
import ErrorBoundary from "../../vbrae-utils/ErrorBoundary.jsx";
import AuthProvider from "../../../providers/AuthProvider.jsx";
import { LanguageProvider } from "../../contexts/LanguageContext.jsx";

const queryClient = new QueryClient();

export default function GlobalLayout(){

    return (
        <ErrorBoundary>
            <QueryClientProvider client={queryClient}>
                <LanguageProvider>
                    <AuthProvider>
                        <Outlet />
                    </AuthProvider>
                </LanguageProvider>
            </QueryClientProvider>
        </ErrorBoundary>
    )
}
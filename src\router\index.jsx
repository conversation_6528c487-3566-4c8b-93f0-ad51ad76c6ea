import {createBrowserRouter, createRoutesFromElements, Route,} from "react-router-dom";
import Home from "../pages/Home";
import Category from "../pages/Category";
import MainLayout from "../pages/layouts/MainLayout";
import ProductDetails from "../pages/ProductDetails";
import Cart from "../pages/Cart";
import CheckOut from "../pages/Checkout";
import Blog from "../pages/Blog";
import BlogDetails from "../pages/BlogDetails";
import Shops from "../pages/Shops";
import ShopDetails from "../pages/ShopDetails";
import HowToSell from "../pages/HowToSell";
import SelectPlan from "../pages/SelectPlan";
import Affiliate from "../pages/Affiliate";
import Contact from "../pages/Contact";
import CouponPartner from "../pages/CouponPartner";
import HelpCenter from "../pages/HelpCenter";
import SubmitRequest from "../pages/SubmitRequest";
import HelpTopic from "../pages/HelpTopic";
import About from "../pages/About";
import PrivacyPolicy from "../pages/PrivacyPolicy";
import Redeem from "../pages/Redeem";
import GiveAway from "../pages/Giveaway";
import Dashboard from "../pages/account/Dashboard";
import AccountLayout from "../pages/layouts/AccountLayout";
import NewOffers from "../pages/account/NewOffers";
import MyOffers from "../pages/account/MyOffers";
import Wishlist from "../pages/account/Wishlist";
import Sales from "../pages/account/Sales";
import Orders from "../pages/account/Orders";
import Balance from "../pages/account/Balance";
import AccountHelpCenter from "../pages/account/HelpCenter";
import Reviews from "../pages/account/Reviews";
import Notifications from "../pages/account/Notifications";
import OfferDetails from "../pages/account/OfferDetails";
import RequestProduct from "../pages/account/RequestProduct";
import SalesDetails from "../pages/account/SalesDetails";
import OrderDetails from "../pages/account/OrderDetails";
import HelpDetails from "../pages/account/HelpDetails";
import Profile from "../pages/account/Profile";
import Messages from "../pages/account/Messages";
import Support from "../pages/account/Support";
import SupportDetails from "../pages/account/Support_Details";
import GlobalLayout from "../pages/layouts/GlobalLayout.jsx";
import OfferEdit from "../pages/account/OfferEdit.jsx";
import Genres from "../pages/Genres.jsx";
import ProtectedLayout from "../pages/layouts/ProtectedLayout.jsx";
import Search from "../pages/Search.jsx";
import ThankYou from "../pages/thankYou.jsx";
import LanguageLayout from "../pages/layouts/LanguageLayout.jsx";
import { supportedLanguages } from "../i18n/index.js";

// Helper function to create routes for all languages
const createLanguageRoutes = () => {
  const routes = [];

  // Create routes for each supported language
  Object.keys(supportedLanguages).forEach(lang => {
    routes.push(
      <Route key={lang} path={`/${lang}`} element={<LanguageLayout />}>
        <Route path="" element={<MainLayout />}>
          <Route index element={<Home />} />
          <Route path="genre/:activeGenre" element={<Genres />} />
          <Route path="search" element={<Search />} />
          <Route path=":activeCategory/:id" element={<Category />} />

          <Route path="details/:id" element={<ProductDetails />} />
          <Route path="blog" element={<Blog />} />
          <Route path="blog-details/:id" element={<BlogDetails />} />
          <Route path="shops" element={<Shops />} />
          <Route path="shop-details/:id" element={<ShopDetails />} />
          <Route path="how-to-sell" element={<HowToSell />} />
          <Route path="select-plan" element={<SelectPlan />} />
          <Route path="affiliate" element={<Affiliate />} />
          <Route path="coupon-partner" element={<CouponPartner />} />
          <Route path="contact" element={<Contact />} />
          <Route path="help-center" element={<HelpCenter />} />
          <Route path="help-topic/:id" element={<HelpTopic />} />
          <Route path="submit-request" element={<SubmitRequest />} />
          <Route path="about" element={<About />} />
          <Route path="privacy-policy" element={<PrivacyPolicy />} />
          <Route path="redeem" element={<Redeem />} />
          <Route path="giveaway" element={<GiveAway />} />

          <Route element={<ProtectedLayout />}>
            <Route path="cart" element={<Cart />} />
            <Route path="checkout" element={<CheckOut />} />
            <Route path="thank-you" element={<ThankYou />} />
          </Route>
        </Route>

        <Route path="account" element={<AccountLayout />}>
          <Route index element={<Dashboard />} />
          <Route path="new-offers" element={<NewOffers />} />
          <Route path="offers" element={<MyOffers />} />
          <Route path="sales" element={<Sales />} />
          <Route path="orders" element={<Orders />} />
          <Route path="balance" element={<Balance />} />
          <Route path="wishlist" element={<Wishlist />} />
          <Route path="help-center" element={<AccountHelpCenter />} />
          <Route path="notifications" element={<Notifications />} />
          <Route path="reviews" element={<Reviews />} />
          <Route path="offer-details/:id" element={<OfferDetails />} />
          <Route path="offer-edit/:id" element={<OfferEdit />} />
          <Route path="request-product" element={<RequestProduct />} />
          <Route path="sales-details/:id" element={<SalesDetails />} />
          <Route path="order-details/:id" element={<OrderDetails />} />
          <Route path="help-details" element={<HelpDetails />} />
          <Route path="profile" element={<Profile />} />
          <Route path="messages" element={<Messages />} />
          <Route path="support" element={<Support />} />
          <Route path="support-details/:id" element={<SupportDetails />} />
        </Route>
      </Route>
    );
  });

  return routes;
};

const Router = createBrowserRouter(
  createRoutesFromElements(
    <Route element={<GlobalLayout />}>
      {/* Language-specific routes */}
      {createLanguageRoutes()}

      {/* Redirect root to default language */}
      <Route path="/" element={<LanguageLayout />} />

      {/* Legacy routes without language prefix - redirect to default language */}
      <Route path="*" element={<LanguageLayout />} />
    </Route>
  )
);

export default Router;

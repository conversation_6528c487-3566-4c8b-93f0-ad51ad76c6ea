import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import HttpApi from 'i18next-http-backend';

// Supported languages
export const supportedLanguages = {
  en: 'English',
  es: 'Español', 
  fr: 'Français',
  lv: 'Latviešu',
  pl: '<PERSON><PERSON>'
};

export const defaultLanguage = 'en';

// Language detection options
const detectionOptions = {
  order: ['path', 'localStorage', 'navigator', 'htmlTag'],
  lookupFromPathIndex: 0,
  lookupFromSubdomainIndex: 0,
  caches: ['localStorage'],
  excludeCacheFor: ['cimode'],
  checkWhitelist: true
};

// Backend options for loading translation files
const backendOptions = {
  loadPath: '/locales/{{lng}}/{{ns}}.json',
  addPath: '/locales/{{lng}}/{{ns}}.missing.json'
};

// Initialize i18next
i18n
  .use(HttpApi)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    // Language settings
    supportedLngs: Object.keys(supportedLanguages),
    fallbackLng: defaultLanguage,
    lng: defaultLanguage,
    
    // Namespace settings
    defaultNS: 'common',
    ns: ['common', 'navigation', 'pages', 'forms', 'errors', 'auth'],
    
    // Detection settings
    detection: detectionOptions,
    
    // Backend settings
    backend: backendOptions,
    
    // React settings
    react: {
      useSuspense: false,
      bindI18n: 'languageChanged',
      bindI18nStore: '',
      transEmptyNodeValue: '',
      transSupportBasicHtmlNodes: true,
      transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'p', 'span', 'a']
    },
    
    // Interpolation settings
    interpolation: {
      escapeValue: false,
      formatSeparator: ',',
      format: function(value, format, lng) {
        if (format === 'uppercase') return value.toUpperCase();
        if (format === 'lowercase') return value.toLowerCase();
        return value;
      }
    },
    
    // Debug settings (disable in production)
    debug: process.env.NODE_ENV === 'development',
    
    // Load settings
    load: 'languageOnly',
    preload: Object.keys(supportedLanguages),
    
    // Whitelist settings
    whitelist: Object.keys(supportedLanguages),
    nonExplicitWhitelist: true,
    
    // Misc settings
    returnEmptyString: false,
    returnNull: false,
    returnObjects: false,
    joinArrays: false,
    
    // Resource settings
    resources: {
      // Will be loaded from backend
    }
  });

// Helper function to get language from path
export const getLanguageFromPath = (pathname) => {
  const segments = pathname.split('/').filter(Boolean);
  const firstSegment = segments[0];
  
  if (firstSegment && Object.keys(supportedLanguages).includes(firstSegment)) {
    return firstSegment;
  }
  
  return defaultLanguage;
};

// Helper function to get path without language prefix
export const getPathWithoutLanguage = (pathname) => {
  const segments = pathname.split('/').filter(Boolean);
  const firstSegment = segments[0];
  
  if (firstSegment && Object.keys(supportedLanguages).includes(firstSegment)) {
    return '/' + segments.slice(1).join('/');
  }
  
  return pathname;
};

// Helper function to add language prefix to path
export const addLanguageToPath = (pathname, language) => {
  const cleanPath = getPathWithoutLanguage(pathname);
  return `/${language}${cleanPath === '/' ? '' : cleanPath}`;
};

// Helper function to change language in current path
export const changeLanguageInPath = (pathname, newLanguage) => {
  const pathWithoutLang = getPathWithoutLanguage(pathname);
  return addLanguageToPath(pathWithoutLang, newLanguage);
};

export default i18n;

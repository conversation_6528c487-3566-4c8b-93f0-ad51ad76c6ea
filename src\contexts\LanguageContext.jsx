import React, { createContext, useContext, useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { 
  supportedLanguages, 
  defaultLanguage, 
  getLanguageFromPath, 
  changeLanguageInPath 
} from '../i18n/index.js';

// Create the Language Context
const LanguageContext = createContext();

// Custom hook to use the Language Context
export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

// Language Provider Component
export const LanguageProvider = ({ children }) => {
  const { i18n } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  const { lang } = useParams();
  
  const [currentLanguage, setCurrentLanguage] = useState(
    lang || getLanguageFromPath(location.pathname) || defaultLanguage
  );
  const [isLoading, setIsLoading] = useState(false);

  // Update current language when route changes
  useEffect(() => {
    const pathLanguage = lang || getLanguageFromPath(location.pathname);
    if (pathLanguage && pathLanguage !== currentLanguage) {
      setCurrentLanguage(pathLanguage);
    }
  }, [lang, location.pathname, currentLanguage]);

  // Sync i18n language with current language
  useEffect(() => {
    if (i18n.language !== currentLanguage) {
      i18n.changeLanguage(currentLanguage);
    }
  }, [currentLanguage, i18n]);

  // Function to change language
  const changeLanguage = async (newLanguage) => {
    if (!Object.keys(supportedLanguages).includes(newLanguage)) {
      console.warn(`Language ${newLanguage} is not supported`);
      return;
    }

    if (newLanguage === currentLanguage) {
      return; // No change needed
    }

    setIsLoading(true);

    try {
      // Change i18n language
      await i18n.changeLanguage(newLanguage);
      
      // Update state
      setCurrentLanguage(newLanguage);
      
      // Navigate to new language path
      const newPath = changeLanguageInPath(location.pathname, newLanguage);
      navigate(newPath + location.search, { replace: true });
      
      // Store language preference
      localStorage.setItem('preferred-language', newLanguage);
      
    } catch (error) {
      console.error('Failed to change language:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Function to get language display name
  const getLanguageDisplayName = (langCode) => {
    return supportedLanguages[langCode] || langCode;
  };

  // Function to get current language display name
  const getCurrentLanguageDisplayName = () => {
    return getLanguageDisplayName(currentLanguage);
  };

  // Function to check if language is supported
  const isLanguageSupported = (langCode) => {
    return Object.keys(supportedLanguages).includes(langCode);
  };

  // Function to get all supported languages
  const getSupportedLanguages = () => {
    return Object.entries(supportedLanguages).map(([code, name]) => ({
      code,
      name,
      isActive: code === currentLanguage
    }));
  };

  // Function to get language-aware URL
  const getLanguageUrl = (path, langCode = currentLanguage) => {
    if (!path.startsWith('/')) {
      path = '/' + path;
    }
    
    // Remove existing language prefix if any
    const cleanPath = path.replace(/^\/[a-z]{2}(\/|$)/, '/');
    
    // Add new language prefix
    return `/${langCode}${cleanPath === '/' ? '' : cleanPath}`;
  };

  // Function to get current path without language prefix
  const getCurrentPathWithoutLanguage = () => {
    return location.pathname.replace(/^\/[a-z]{2}(\/|$)/, '/') || '/';
  };

  // Context value
  const contextValue = {
    // Current language state
    currentLanguage,
    isLoading,
    
    // Language information
    supportedLanguages,
    defaultLanguage,
    
    // Language functions
    changeLanguage,
    getLanguageDisplayName,
    getCurrentLanguageDisplayName,
    isLanguageSupported,
    getSupportedLanguages,
    
    // URL functions
    getLanguageUrl,
    getCurrentPathWithoutLanguage,
    
    // i18n instance for advanced usage
    i18n
  };

  return (
    <LanguageContext.Provider value={contextValue}>
      {children}
    </LanguageContext.Provider>
  );
};

export default LanguageContext;

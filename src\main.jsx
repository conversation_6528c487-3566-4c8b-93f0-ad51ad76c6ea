import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { RouterProvider } from "react-router-dom";
import Router from "./router/index.jsx";
import "../public/assets/css/main.css";
import "../public/assets/css/responsive.css";
import "../public/assets/css/plugins.css";
import { NavProvider } from "./store/NavContext.jsx";
import { ModalProvider } from "./store/ModalContext.jsx";
import { SideBarProvider } from "./store/SideBarContext.jsx";
import "./i18n/index.js"; // Initialize i18n

createRoot(document.getElementById("root")).render(
  <StrictMode>
    <ModalProvider>
      <NavProvider>
       <SideBarProvider>
       <RouterProvider router={Router} />
       </SideBarProvider>
      </NavProvider>
    </ModalProvider>
  </StrictMode>
);
